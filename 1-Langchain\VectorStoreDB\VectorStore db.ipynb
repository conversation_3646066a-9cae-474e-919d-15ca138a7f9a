##building a sample vectordb
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import TextLoader
from langchain_ollama import OllamaEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter


loader = TextLoader("cleaned_text.txt")
data = loader.load()
data

text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
docs = text_splitter.split_documents(data)
docs

## Using the default model
embeddings=(
    OllamaEmbeddings(model="qwen2.5:1.5b")
)
embeddings

from langchain.embeddings import OllamaEmbeddings

# Load the embedding model
embedding_model = OllamaEmbeddings(model="qwen2.5:1.5b")

# Embed the text chunks
text_chunks = [docs] 
embedded_texts = embedding_model.embed_documents(text_chunks)
embedded_texts


import faiss
import pickle
import os
import numpy as np
from langchain_community.vectorstores import FAISS
from langchain.docstore import InMemoryDocstore
from langchain_community.embeddings import OllamaEmbeddings

# Example: Ensure embedded_texts is a valid list of float vectors
embedded_texts = np.array(embedded_texts, dtype="float32")

# Create FAISS index
dimension = embedded_texts.shape[1]
index = faiss.IndexFlatL2(dimension)  # L2 distance for similarity search
index.add(embedded_texts)  # Add embeddings to the FAISS index

docstore = InMemoryDocstore({str(i): doc for i, doc in enumerate(docs)})
index_to_docstore_id = {i: str(i) for i in range(len(docs))} 

# Define save directory
save_dir = r"C:\Users\<USER>\OneDrive\Desktop\Langchain\1-Langchain\VectorStoreDB"
os.makedirs(save_dir, exist_ok=True)

# Save FAISS index
index_path = os.path.join(save_dir, "index.faiss")
faiss.write_index(index, index_path)

# Save metadata (docstore and index_to_docstore_id)
metadata_path = os.path.join(save_dir, "index.pkl")
metadata = {"docstore": {}, "index_to_docstore_id": {}}  # Initialize empty metadata for now
with open(metadata_path, "wb") as f:
    pickle.dump(metadata, f)

print("✅ FAISS index and metadata saved successfully!")


# Load FAISS index
index = faiss.read_index(index_path)

# Load metadata
with open(metadata_path, "rb") as f:
    saved_data = pickle.load(f)

# Reconstruct FAISS vector store
embedding_function = OllamaEmbeddings(model="qwen2.5:1.5b")  # Ensure correct embedding function

vector_store = FAISS(
    index=index,
    docstore=saved_data.get("docstore", {}),  # Use empty dict if missing
    index_to_docstore_id=saved_data.get("index_to_docstore_id", {}),
    embedding_function=embedding_function
)

print("✅ FAISS index and metadata loaded successfully!")


retriever = vector_store.as_retriever(search_type="similarity")
retriever

from langchain_ollama import OllamaLLM

# Initialize the LLM
llm = OllamaLLM(model="qwen2.5:1.5b")

# Test the LLM with a simple query
response = llm.invoke("what are drugs and doses for turboculosis treatment?")
print("LLM Response:", response)

